/**
 * 数据冲突解决器
 * 处理本地数据与云端数据的冲突问题
 */

class DataConflictResolver {
  constructor() {
    // 冲突解决策略
    this.strategies = {
      'travel_plan': this.resolveTravelPlanConflict.bind(this),
      'expense_record': this.resolveExpenseRecordConflict.bind(this),
      'user_data': this.resolveUserDataConflict.bind(this)
    }
    
    // 冲突历史记录
    this.conflictHistory = []
  }

  /**
   * 解决数据冲突
   * @param {string} dataType 数据类型
   * @param {Object} localData 本地数据
   * @param {Object} remoteData 远程数据
   * @returns {Object} 解决结果
   */
  async resolveConflict(dataType, localData, remoteData) {
    try {
      // 检查是否真的存在冲突
      if (!this.hasConflict(localData, remoteData)) {
        return {
          success: true,
          resolved: true,
          data: remoteData,
          strategy: 'no_conflict'
        }
      }

      // 记录冲突
      this.recordConflict(dataType, localData, remoteData)

      // 选择解决策略
      const strategy = this.strategies[dataType] || this.resolveGenericConflict.bind(this)
      const result = await strategy(localData, remoteData)

      return {
        success: true,
        resolved: true,
        data: result.data,
        strategy: result.strategy,
        conflicts: result.conflicts || []
      }

    } catch (error) {
      console.error('解决数据冲突失败:', error)
      return {
        success: false,
        resolved: false,
        error: error.message,
        data: remoteData // 默认使用远程数据
      }
    }
  }

  /**
   * 检查是否存在冲突
   * @param {Object} localData 本地数据
   * @param {Object} remoteData 远程数据
   * @returns {boolean} 是否存在冲突
   */
  hasConflict(localData, remoteData) {
    if (!localData || !remoteData) return false

    // 比较更新时间
    const localTime = new Date(localData.updateTime || localData.lastModified || 0)
    const remoteTime = new Date(remoteData.updateTime || remoteData.lastModified || 0)

    // 如果时间差小于1秒，认为没有冲突
    if (Math.abs(localTime - remoteTime) < 1000) {
      return false
    }

    // 比较关键字段
    const keyFields = ['title', 'amount', 'status', 'destination']
    return keyFields.some(field => {
      if (localData[field] !== undefined && remoteData[field] !== undefined) {
        return localData[field] !== remoteData[field]
      }
      return false
    })
  }

  /**
   * 解决旅行计划冲突
   * @param {Object} localData 本地数据
   * @param {Object} remoteData 远程数据
   * @returns {Object} 解决结果
   */
  async resolveTravelPlanConflict(localData, remoteData) {
    const conflicts = []
    const mergedData = { ...remoteData }

    // 比较更新时间，选择较新的数据
    const localTime = new Date(localData.updateTime || 0)
    const remoteTime = new Date(remoteData.updateTime || 0)

    // 字段级别的冲突解决
    const conflictFields = ['title', 'destination', 'startDate', 'endDate', 'budget', 'notes']
    
    conflictFields.forEach(field => {
      if (localData[field] !== remoteData[field]) {
        conflicts.push({
          field,
          localValue: localData[field],
          remoteValue: remoteData[field]
        })

        // 使用时间戳较新的值
        if (localTime > remoteTime) {
          mergedData[field] = localData[field]
        }
      }
    })

    // 特殊处理：协作者列表合并
    if (localData.collaboration && remoteData.collaboration) {
      mergedData.collaboration = this.mergeCollaborationData(
        localData.collaboration,
        remoteData.collaboration
      )
    }

    return {
      data: mergedData,
      strategy: 'timestamp_priority',
      conflicts
    }
  }

  /**
   * 解决支出记录冲突
   * @param {Object} localData 本地数据
   * @param {Object} remoteData 远程数据
   * @returns {Object} 解决结果
   */
  async resolveExpenseRecordConflict(localData, remoteData) {
    const conflicts = []
    const mergedData = { ...remoteData }

    // 支出记录通常以远程数据为准，但保留本地的某些字段
    const localPreferredFields = ['notes', 'tags']
    
    localPreferredFields.forEach(field => {
      if (localData[field] && localData[field] !== remoteData[field]) {
        conflicts.push({
          field,
          localValue: localData[field],
          remoteValue: remoteData[field]
        })
        
        // 如果本地有值而远程没有，使用本地值
        if (!remoteData[field]) {
          mergedData[field] = localData[field]
        }
      }
    })

    return {
      data: mergedData,
      strategy: 'remote_priority_with_local_notes',
      conflicts
    }
  }

  /**
   * 解决用户数据冲突
   * @param {Object} localData 本地数据
   * @param {Object} remoteData 远程数据
   * @returns {Object} 解决结果
   */
  async resolveUserDataConflict(localData, remoteData) {
    // 用户数据以远程为准
    return {
      data: remoteData,
      strategy: 'remote_priority',
      conflicts: []
    }
  }

  /**
   * 通用冲突解决
   * @param {Object} localData 本地数据
   * @param {Object} remoteData 远程数据
   * @returns {Object} 解决结果
   */
  async resolveGenericConflict(localData, remoteData) {
    // 默认策略：使用远程数据
    return {
      data: remoteData,
      strategy: 'remote_priority_default',
      conflicts: []
    }
  }

  /**
   * 合并协作数据
   * @param {Object} localCollaboration 本地协作数据
   * @param {Object} remoteCollaboration 远程协作数据
   * @returns {Object} 合并后的协作数据
   */
  mergeCollaborationData(localCollaboration, remoteCollaboration) {
    const merged = { ...remoteCollaboration }

    // 合并协作者列表（去重）
    if (localCollaboration.collaborators && remoteCollaboration.collaborators) {
      const collaboratorMap = new Map()
      
      // 先添加远程协作者
      remoteCollaboration.collaborators.forEach(collaborator => {
        collaboratorMap.set(collaborator.openid, collaborator)
      })
      
      // 再添加本地协作者（如果不存在）
      localCollaboration.collaborators.forEach(collaborator => {
        if (!collaboratorMap.has(collaborator.openid)) {
          collaboratorMap.set(collaborator.openid, collaborator)
        }
      })
      
      merged.collaborators = Array.from(collaboratorMap.values())
    }

    return merged
  }

  /**
   * 记录冲突
   * @param {string} dataType 数据类型
   * @param {Object} localData 本地数据
   * @param {Object} remoteData 远程数据
   */
  recordConflict(dataType, localData, remoteData) {
    const conflict = {
      id: Date.now().toString(),
      dataType,
      timestamp: new Date().toISOString(),
      localUpdateTime: localData.updateTime,
      remoteUpdateTime: remoteData.updateTime
    }

    this.conflictHistory.push(conflict)

    // 限制历史记录数量
    if (this.conflictHistory.length > 100) {
      this.conflictHistory = this.conflictHistory.slice(-50)
    }
  }

  /**
   * 获取冲突历史
   * @returns {Array} 冲突历史记录
   */
  getConflictHistory() {
    return this.conflictHistory
  }

  /**
   * 清理冲突历史
   */
  clearConflictHistory() {
    this.conflictHistory = []
  }

  /**
   * 获取冲突统计
   * @returns {Object} 冲突统计
   */
  getConflictStats() {
    const stats = {
      totalConflicts: this.conflictHistory.length,
      recentConflicts: 0,
      conflictsByType: {}
    }

    const oneHourAgo = Date.now() - 60 * 60 * 1000

    this.conflictHistory.forEach(conflict => {
      // 统计最近一小时的冲突
      if (new Date(conflict.timestamp).getTime() > oneHourAgo) {
        stats.recentConflicts++
      }

      // 按类型统计
      stats.conflictsByType[conflict.dataType] = 
        (stats.conflictsByType[conflict.dataType] || 0) + 1
    })

    return stats
  }
}

// 创建全局实例
const dataConflictResolver = new DataConflictResolver()

export default dataConflictResolver
export { DataConflictResolver }
