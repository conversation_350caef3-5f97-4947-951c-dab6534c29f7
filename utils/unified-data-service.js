/**
 * 统一数据服务 - 精简版
 * 专注解决travel云函数过度调用问题，避免功能臃肿
 */

// 导入现有模块
import cacheManager, { CACHE_KEYS } from './cache-manager.js'
import auth from './auth.js'
import localTravelCalculator from './local-travel-calculator.js'

// 数据库集合定义
const COLLECTIONS = {
  USERS: 'users',
  RECORDS: 'expense_records',
  TRAVEL_PLANS: 'travel_plans',
  CATEGORIES: 'categories',
  USER_BUDGETS: 'user_budgets'
}

// 优化后的缓存TTL定义（毫秒）
const CACHE_TTL = {
  INSTANT: 30 * 1000,      // 30秒 - 内存缓存
  SHORT: 5 * 60 * 1000,    // 5分钟 - 本地存储缓存
  MEDIUM: 15 * 60 * 1000,  // 15分钟 - 稳定数据
  LONG: 60 * 60 * 1000     // 1小时 - 很少变化的数据
}

// 数据版本控制
const DATA_VERSIONS = {
  TRAVEL_PLANS: 'travel_plans_v',
  STATISTICS: 'statistics_v',
  CURRENT_PLAN: 'current_plan_v'
}

class UnifiedDataService {
  constructor() {
    // 使用统一的缓存管理器
    this.cacheManager = cacheManager

    // 本地计算引擎
    this.localCalculator = localTravelCalculator

    // 同步状态管理（简化版）
    this.syncStatus = {
      isOnline: true,
      lastSyncTime: 0,
      syncInProgress: false
    }

    // 同步队列（简化版）
    this.syncQueue = []
    this.syncTimer = null

    // 初始化
    this.init()
  }

  /**
   * 初始化服务
   */
  async init() {
    // 检查网络状态
    this.checkNetworkStatus()

    // 监听网络状态变化
    wx.onNetworkStatusChange((res) => {
      this.syncStatus.isOnline = res.isConnected
    })

    // 启动智能同步（5分钟间隔）
    this.startIntelligentSync()

    // 延迟预加载关键数据，确保用户体验
    setTimeout(() => {
      this.preloadCriticalData()
    }, 500) // 500ms后预加载，不阻塞初始化
  }

  /**
   * 检查网络状态
   */
  checkNetworkStatus() {
    wx.getNetworkType({
      success: (res) => {
        this.syncStatus.isOnline = res.networkType !== 'none'
      }
    })
  }



  /**
   * 智能预加载关键数据
   */
  async preloadCriticalData() {
    try {
      // 只预加载最关键的数据，确保首次访问速度
      const preloadPromises = []

      // 预加载旅行统计（优先级最高）
      preloadPromises.push(
        this.getTravelStatistics().catch(error => {
          console.warn('预加载旅行统计失败:', error)
          return null
        })
      )

      // 预加载最近的旅行计划（限制3个）
      preloadPromises.push(
        this.getTravelPlans({ limit: 3 }).catch(error => {
          console.warn('预加载旅行计划失败:', error)
          return null
        })
      )

      // 并行执行，不等待结果
      Promise.allSettled(preloadPromises).then(() => {
        console.log('关键数据预加载完成')
      })

    } catch (error) {
      console.error('预加载过程出错:', error)
    }
  }

  /**
   * 启动智能同步 - 5分钟间隔，减少频繁调用
   */
  startIntelligentSync() {
    if (this.syncTimer) {
      clearInterval(this.syncTimer)
    }

    // 5分钟同步一次，大幅减少调用频率
    this.syncTimer = setInterval(() => {
      if (this.syncStatus.isOnline && !this.syncStatus.syncInProgress) {
        this.processSyncQueue()
      }
    }, 5 * 60 * 1000) // 5分钟
  }



  // ==================== 统一云函数调用 ====================

  /**
   * 统一云函数调用（带超时和重试机制）
   * @param {string} name 云函数名称
   * @param {string} action 操作类型
   * @param {Object} data 数据
   * @param {Object} options 选项
   */
  async callFunction(name, action, data = {}, options = {}) {
    const {
      useCache = true,
      cacheKey,
      cacheTTL = CACHE_TTL.MEDIUM,
      forceRefresh = false,
      timeout = 5000,
      retries = 2
    } = options

    try {
      // 检查缓存
      if (useCache && cacheKey && !forceRefresh) {
        const cached = this.cacheManager.get(cacheKey)
        if (cached) {
          return { success: true, data: cached, fromCache: true }
        }
      }

      // 执行云函数调用（带超时和重试）
      const result = await this.executeWithRetry(
        () => this.callCloudFunction(name, action, data, timeout),
        retries
      )

      if (result.result && result.result.success) {
        // 缓存结果
        if (useCache && cacheKey) {
          this.cacheManager.set(cacheKey, result.result.data, cacheTTL)
        }

        return result.result
      } else {
        throw new Error(result.result?.message || '云函数调用失败')
      }
    } catch (error) {
      console.error(`云函数调用失败 [${name}/${action}]:`, error)
      return {
        success: false,
        message: error.message || '数据获取失败'
      }
    }
  }

  /**
   * 执行云函数调用（带超时）
   */
  async callCloudFunction(name, action, data, timeout) {
    return Promise.race([
      wx.cloud.callFunction({
        name,
        data: { action, data }
      }),
      new Promise((_, reject) =>
        setTimeout(() => reject(new Error('云函数调用超时')), timeout)
      )
    ])
  }

  /**
   * 带重试机制的执行
   */
  async executeWithRetry(fn, retries) {
    let lastError
    
    for (let i = 0; i <= retries; i++) {
      try {
        return await fn()
      } catch (error) {
        lastError = error
        if (i < retries) {
          // 等待后重试
          await new Promise(resolve => setTimeout(resolve, 1000 * (i + 1)))
        }
      }
    }
    
    throw lastError
  }

  // ==================== 统一缓存管理 ====================

  /**
   * 设置缓存
   */
  setCache(key, data, ttl = CACHE_TTL.MEDIUM) {
    return this.cacheManager.set(key, data, ttl)
  }

  /**
   * 获取缓存
   */
  getCache(key) {
    return this.cacheManager.get(key)
  }

  /**
   * 清除缓存（统一入口）
   */
  clearCache(pattern = null) {
    if (pattern) {
      this.cacheManager.clearByPattern(pattern)
    } else {
      this.cacheManager.clear()
    }

    // 同步清理本地存储
    this.clearLocalStorage(pattern)
  }

  /**
   * 清理本地存储
   */
  clearLocalStorage(pattern = null) {
    if (pattern) {
      const keysToClean = [
        `${pattern}_data`,
        `${pattern}_statistics`,
        `${pattern}_overview`,
        `homepage_${pattern}_data`
      ]
      keysToClean.forEach(key => {
        try {
          wx.removeStorageSync(key)
        } catch (error) {
          // 静默处理
        }
      })
    } else {
      // 清理所有相关存储
      const allKeys = [
        'travel_page_data',
        'travel_statistics',
        'financial_overview',
        'homepage_financial_data',
        'homepage_travel_data'
      ]
      allKeys.forEach(key => {
        try {
          wx.removeStorageSync(key)
        } catch (error) {
          // 静默处理
        }
      })
    }
  }

  // ==================== 数据验证 ====================

  /**
   * 验证财务数据
   */
  validateFinancialData(data) {
    if (!data || typeof data !== 'object') return false
    
    const requiredFields = ['totalExpense', 'monthlyExpense', 'budgetUsage']
    return requiredFields.every(field => 
      data.hasOwnProperty(field) && typeof data[field] === 'number'
    )
  }

  /**
   * 验证旅行数据
   */
  validateTravelData(data) {
    if (!data || typeof data !== 'object') return false
    
    const requiredFields = ['totalPlans', 'ongoingPlans', 'totalExpense']
    return requiredFields.every(field => 
      data.hasOwnProperty(field) && typeof data[field] === 'number'
    )
  }

  // ==================== 业务数据接口 ====================

  /**
   * 获取财务概览
   */
  async getFinancialOverview(forceRefresh = false) {
    return await this.callFunction('expense', 'getFinancialOverview', {}, {
      cacheKey: CACHE_KEYS.FINANCIAL_OVERVIEW,
      cacheTTL: CACHE_TTL.MEDIUM,
      forceRefresh
    })
  }

  /**
   * 获取旅行统计 - 本地优先策略，确保快速响应
   */
  async getTravelStatistics(forceRefresh = false) {
    try {
      const cacheKey = CACHE_KEYS.TRAVEL_STATISTICS

      // 1. 优先从缓存获取（立即响应）
      if (!forceRefresh) {
        const cached = this.cacheManager.get(cacheKey)
        if (cached) {
          return { success: true, data: cached, fromCache: true }
        }
      }

      // 2. 尝试本地计算（快速响应）
      const localPlans = this.getLocalTravelPlans()
      const localExpenses = this.getLocalExpenseRecords()

      if (localPlans.length > 0 && this.localCalculator) {
        const localStats = this.localCalculator.calculateStatistics(localPlans, localExpenses)

        // 立即缓存本地计算结果
        this.cacheManager.set(cacheKey, localStats, CACHE_TTL.INSTANT)

        // 后台静默同步，不影响用户体验
        setTimeout(() => {
          this.queueBackgroundSync('travel_statistics')
        }, 100)

        return { success: true, data: localStats, fromLocal: true }
      }

      // 3. 云函数调用（带超时保护）
      const cloudResult = await Promise.race([
        this.callFunction('travel', 'getTravelStatistics', {
          includeCollaboration: true
        }, {
          cacheKey,
          cacheTTL: CACHE_TTL.SHORT,
          forceRefresh
        }),
        new Promise((_, reject) =>
          setTimeout(() => reject(new Error('请求超时')), 8000)
        )
      ])

      return cloudResult

    } catch (error) {
      console.error('获取旅行统计失败:', error)

      // 错误时返回默认数据，确保界面正常显示
      const defaultStats = {
        totalPlans: 0,
        completedPlans: 0,
        ongoingPlans: 0,
        plannedPlans: 0,
        totalExpense: 0,
        thisMonthExpense: 0,
        avgExpensePerTrip: 0,
        popularDestinations: []
      }

      return { success: true, data: defaultStats, fromDefault: true }
    }
  }

  /**
   * 获取旅行计划列表 - 本地优先策略
   */
  async getTravelPlans(options = {}) {
    try {
      const { status, limit, forceRefresh = false } = options
      const cacheKey = `${CACHE_KEYS.TRAVEL_PLANS}_${status || 'all'}`

      // 1. 优先从缓存获取
      if (!forceRefresh) {
        const cached = this.cacheManager.get(cacheKey)
        if (cached) {
          return { success: true, data: cached, fromCache: true }
        }
      }

      // 2. 尝试从本地数据计算
      const localPlans = this.getLocalTravelPlans()
      if (localPlans.length > 0 && this.localCalculator) {
        let filteredPlans = localPlans

        // 本地状态过滤
        if (status) {
          filteredPlans = localPlans.filter(plan =>
            this.localCalculator.calculatePlanStatus(plan) === status
          )
        }

        // 限制数量
        if (limit) {
          filteredPlans = filteredPlans.slice(0, limit)
        }

        // 缓存结果
        this.cacheManager.set(cacheKey, filteredPlans, CACHE_TTL.INSTANT)

        // 后台静默同步
        this.queueBackgroundSync('travel_plans', { status, limit })

        return { success: true, data: filteredPlans, fromLocal: true }
      }

      // 3. 最后才调用云函数
      return await this.callFunction('travel', 'getTravelPlans', { status, limit }, {
        cacheKey,
        cacheTTL: CACHE_TTL.SHORT,
        forceRefresh
      })

    } catch (error) {
      console.error('获取旅行计划失败:', error)
      return { success: false, message: error.message }
    }
  }

  // ==================== 本地数据管理 ====================

  /**
   * 获取本地旅行计划数据
   */
  getLocalTravelPlans() {
    try {
      const cached = wx.getStorageSync('local_travel_plans')
      return cached ? JSON.parse(cached) : []
    } catch (error) {
      console.error('获取本地旅行计划失败:', error)
      return []
    }
  }

  /**
   * 获取本地支出记录数据
   */
  getLocalExpenseRecords() {
    try {
      const cached = wx.getStorageSync('local_expense_records')
      return cached ? JSON.parse(cached) : []
    } catch (error) {
      console.error('获取本地支出记录失败:', error)
      return []
    }
  }

  /**
   * 更新本地数据
   */
  updateLocalData(dataType, data) {
    try {
      const key = `local_${dataType}`
      wx.setStorageSync(key, JSON.stringify(data))

      // 更新内存缓存
      this.localData.set(dataType, data)

      return true
    } catch (error) {
      console.error('更新本地数据失败:', error)
      return false
    }
  }

  /**
   * 队列后台同步 - 批量处理减少调用
   */
  queueBackgroundSync(dataType, params = {}) {
    this.syncQueue.push({
      dataType,
      params,
      timestamp: Date.now()
    })

    // 如果队列过长，立即处理
    if (this.syncQueue.length >= 3) {
      this.processSyncQueue()
    }
  }

  /**
   * 处理同步队列
   */
  async processSyncQueue() {
    if (this.syncQueue.length === 0 || this.syncStatus.syncInProgress) {
      return
    }

    this.syncStatus.syncInProgress = true

    try {
      // 去重同步任务
      const uniqueTasks = this.deduplicateSyncTasks(this.syncQueue)
      this.syncQueue = []

      // 批量执行同步
      const syncPromises = uniqueTasks.map(task => this.executeSyncTask(task))
      await Promise.allSettled(syncPromises)

      this.syncStatus.lastSyncTime = Date.now()

    } catch (error) {
      console.error('处理同步队列失败:', error)
    } finally {
      this.syncStatus.syncInProgress = false
    }
  }

  /**
   * 去重同步任务
   */
  deduplicateSyncTasks(tasks) {
    const taskMap = new Map()

    tasks.forEach(task => {
      const key = `${task.dataType}_${JSON.stringify(task.params)}`
      if (!taskMap.has(key) || taskMap.get(key).timestamp < task.timestamp) {
        taskMap.set(key, task)
      }
    })

    return Array.from(taskMap.values())
  }

  /**
   * 执行单个同步任务
   */
  async executeSyncTask(task) {
    try {
      const { dataType, params } = task

      switch (dataType) {
        case 'travel_statistics':
          return await this.callFunction('travel', 'getTravelStatistics', {
            includeCollaboration: true
          })
        case 'travel_plans':
          return await this.callFunction('travel', 'getTravelPlans', params)
        default:
          console.warn('未知的同步任务类型:', dataType)
      }
    } catch (error) {
      console.error('执行同步任务失败:', error)
    }
  }

  /**
   * 处理待处理操作
   */
  async processPendingOperations() {
    if (this.syncStatus.pendingOperations.length === 0) {
      return
    }

    const operations = [...this.syncStatus.pendingOperations]
    this.syncStatus.pendingOperations = []

    for (const operation of operations) {
      try {
        await this.callFunction(operation.name, operation.action, operation.data)
      } catch (error) {
        console.error('处理待处理操作失败:', error)
        // 重新加入队列
        this.syncStatus.pendingOperations.push(operation)
      }
    }
  }

  /**
   * 获取服务状态
   */
  getStatus() {
    return {
      syncStatus: this.syncStatus,
      cacheStats: this.cacheManager.getStats(),
      dataVersions: Object.fromEntries(this.dataVersions),
      syncQueueLength: this.syncQueue.length,
      localDataSize: this.localData.size
    }
  }

  /**
   * 清理异常数据
   */
  async cleanupData() {
    try {
      // 清除所有缓存
      this.clearCache()

      // 清除本地数据
      this.localData.clear()

      // 清除同步队列
      this.syncQueue = []

      return { success: true, message: '数据清理完成' }
    } catch (error) {
      return { success: false, message: '数据清理失败' }
    }
  }

  /**
   * 数据预热 - 预加载常用数据
   */
  async warmupData() {
    try {
      console.log('开始数据预热...')

      // 并行预热关键数据
      const warmupTasks = [
        this.getFinancialOverview().catch(e => console.warn('财务数据预热失败:', e)),
        this.getTravelStatistics().catch(e => console.warn('旅行统计预热失败:', e)),
        this.getTravelPlans({ limit: 5 }).catch(e => console.warn('旅行计划预热失败:', e))
      ]

      await Promise.allSettled(warmupTasks)
      console.log('数据预热完成')

      return { success: true, message: '数据预热完成' }
    } catch (error) {
      console.error('数据预热失败:', error)
      return { success: false, message: '数据预热失败' }
    }
  }

  /**
   * 智能数据同步 - 根据数据变化智能更新缓存
   */
  async smartSync(dataType, newData) {
    try {
      // 检查数据是否真的发生了变化
      const cacheKey = this.getCacheKeyForDataType(dataType)
      const cachedData = this.getCache(cacheKey)

      if (cachedData && JSON.stringify(cachedData) === JSON.stringify(newData)) {
        // 数据没有变化，无需更新
        return { success: true, message: '数据无变化，跳过同步' }
      }

      // 更新缓存
      this.setCache(cacheKey, newData)

      // 更新数据版本
      this.dataVersions.set(dataType, Date.now())

      return { success: true, message: '数据同步完成' }
    } catch (error) {
      return { success: false, message: '数据同步失败' }
    }
  }

  /**
   * 根据数据类型获取缓存键
   */
  getCacheKeyForDataType(dataType) {
    const keyMap = {
      'financial': CACHE_KEYS.FINANCIAL_OVERVIEW,
      'travel': CACHE_KEYS.TRAVEL_STATISTICS,
      'plans': CACHE_KEYS.TRAVEL_PLANS,
      'user': CACHE_KEYS.USER_INFO
    }
    return keyMap[dataType] || dataType
  }
}

// 创建全局实例
const unifiedDataService = new UnifiedDataService()

// 导出常量和实例
export { COLLECTIONS, CACHE_KEYS, CACHE_TTL }
export default unifiedDataService
