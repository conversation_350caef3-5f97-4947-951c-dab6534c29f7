/**
 * 实时同步管理器
 * 解决数据同步问题，确保旅行计划数据在首页和各页面实时更新
 */
class RealtimeSyncManager {
  constructor() {
    // 同步监听器映射
    this.syncListeners = new Map()
    
    // 同步定时器
    this.syncTimers = new Map()
    
    // 数据版本控制
    this.dataVersions = new Map()
    
    // 同步状态
    this.syncStatus = {
      isActive: false,
      lastSyncTime: 0,
      syncCount: 0
    }
    
    // 同步配置 - 大幅减少调用频率
    this.config = {
      syncInterval: 5 * 60 * 1000,  // 5分钟同步间隔（从30秒改为5分钟）
      maxRetries: 2,                // 最大重试次数减少
      retryDelay: 2000,             // 重试延迟增加
      batchSize: 3,                 // 批量同步大小减少
      minSyncInterval: 2 * 60 * 1000 // 最小同步间隔2分钟，防止过度调用
    }

    // 防重复调用状态
    this.syncInProgress = new Set() // 记录正在同步的数据类型

    // 页面可见性状态
    this.pageVisibility = {
      isVisible: true,
      visiblePages: new Set()
    }

    // 初始化全局数据变更监听
    this.initGlobalDataListener()

    // 初始化页面可见性监听
    this.initPageVisibilityListener()
  }

  /**
   * 注册同步监听器
   * @param {string} dataType 数据类型 (travel_plans, current_plan, ongoing_plans)
   * @param {string} identifier 标识符 (页面路径或组件ID)
   * @param {Function} callback 回调函数
   */
  registerSync(dataType, identifier, callback) {
    const key = `${dataType}_${identifier}`

    if (!this.syncListeners.has(dataType)) {
      this.syncListeners.set(dataType, new Map())
    }

    const listeners = this.syncListeners.get(dataType)

    // 检查是否已经注册过相同的监听器
    if (listeners.has(identifier)) {
      console.log(`同步监听器已存在: ${dataType}/${identifier}，跳过重复注册`)
      return
    }

    listeners.set(identifier, {
      callback,
      lastUpdate: 0,
      retryCount: 0
    })

    console.log(`注册同步监听器: ${dataType}/${identifier}，当前监听器数量: ${listeners.size}`)

    // 只有第一个监听器时才启动定时同步
    if (listeners.size === 1) {
      this.startSync(dataType)
      // 延迟执行首次同步，避免页面加载时的大量并发调用
      setTimeout(() => {
        this.performSync(dataType)
      }, 2000)
    }
  }

  /**
   * 取消同步监听器
   * @param {string} dataType 数据类型
   * @param {string} identifier 标识符
   */
  unregisterSync(dataType, identifier) {
    if (this.syncListeners.has(dataType)) {
      const listeners = this.syncListeners.get(dataType)
      const deleted = listeners.delete(identifier)

      if (deleted) {
        console.log(`取消同步监听器: ${dataType}/${identifier}，剩余监听器数量: ${listeners.size}`)
      }

      // 如果该数据类型没有监听器了，停止同步
      if (listeners.size === 0) {
        console.log(`停止同步: ${dataType}，无剩余监听器`)
        this.stopSync(dataType)
      }
    }
  }

  /**
   * 启动指定数据类型的同步
   * @param {string} dataType 数据类型
   */
  startSync(dataType) {
    if (this.syncTimers.has(dataType)) {
      return // 已经在同步中
    }
    
    const timer = setInterval(() => {
      this.performSync(dataType)
    }, this.config.syncInterval)
    
    this.syncTimers.set(dataType, timer)
  }

  /**
   * 停止指定数据类型的同步
   * @param {string} dataType 数据类型
   */
  stopSync(dataType) {
    if (this.syncTimers.has(dataType)) {
      clearInterval(this.syncTimers.get(dataType))
      this.syncTimers.delete(dataType)
    }
  }

  /**
   * 执行同步操作
   * @param {string} dataType 数据类型
   */
  async performSync(dataType) {
    try {
      // 检查页面可见性
      if (!this.shouldSync()) {
        console.log(`同步跳过: ${dataType} 页面不可见`)
        return
      }

      // 防重复调用检查
      if (this.syncInProgress.has(dataType)) {
        console.log(`同步跳过: ${dataType} 正在进行中`)
        return
      }

      // 检查最小同步间隔
      const lastSyncTime = this.syncStatus.lastSyncTime || 0
      const now = Date.now()
      if (now - lastSyncTime < this.config.minSyncInterval) {
        console.log(`同步跳过: ${dataType} 距离上次同步不足${this.config.minSyncInterval/1000/60}分钟`)
        return
      }

      const listeners = this.syncListeners.get(dataType)
      if (!listeners || listeners.size === 0) {
        return
      }

      // 标记同步开始
      this.syncInProgress.add(dataType)

      // 获取最新数据
      const syncData = await this.fetchSyncData(dataType)
      
      if (!syncData.success) {
        console.error(`同步失败: ${dataType}`, syncData.error)
        return
      }

      // 检查数据是否有更新
      const currentVersion = this.dataVersions.get(dataType) || 0
      const newVersion = syncData.version || Date.now()
      
      if (newVersion <= currentVersion && !syncData.forceUpdate) {
        return // 数据没有更新
      }

      // 更新数据版本
      this.dataVersions.set(dataType, newVersion)
      
      // 通知所有监听器
      const notifyPromises = []
      listeners.forEach((listener, identifier) => {
        notifyPromises.push(this.notifyListener(dataType, identifier, listener, syncData.data))
      })

      await Promise.allSettled(notifyPromises)

      // 更新同步状态
      this.syncStatus.lastSyncTime = Date.now()
      this.syncStatus.syncCount++

    } catch (error) {
      console.error(`同步异常: ${dataType}`, error)
    } finally {
      // 清除同步标记
      this.syncInProgress.delete(dataType)
    }
  }

  /**
   * 获取同步数据
   * @param {string} dataType 数据类型
   */
  async fetchSyncData(dataType) {
    try {
      const lastVersion = this.dataVersions.get(dataType) || 0
      
      let result
      switch (dataType) {
        case 'travel_plans':
          result = await wx.cloud.callFunction({
            name: 'travel',
            data: {
              action: 'getTravelPlans',
              data: { 
                limit: 10,
                lastVersion 
              }
            }
          })
          break
          
        case 'current_plan':
          result = await wx.cloud.callFunction({
            name: 'travel',
            data: {
              action: 'getCurrentPlan',
              data: { lastVersion }
            }
          })
          break
          
        case 'ongoing_plans':
          result = await wx.cloud.callFunction({
            name: 'travel',
            data: {
              action: 'getOngoingPlans',
              data: { lastVersion }
            }
          })
          break
          
        default:
          throw new Error(`不支持的数据类型: ${dataType}`)
      }

      if (result.result && result.result.success) {
        return {
          success: true,
          data: result.result.data,
          version: result.result.version || Date.now(),
          forceUpdate: result.result.forceUpdate || false
        }
      } else {
        throw new Error(result.result?.message || '获取数据失败')
      }
      
    } catch (error) {
      return {
        success: false,
        error: error.message
      }
    }
  }

  /**
   * 通知监听器
   * @param {string} dataType 数据类型
   * @param {string} identifier 标识符
   * @param {Object} listener 监听器对象
   * @param {any} data 数据
   */
  async notifyListener(dataType, identifier, listener, data) {
    try {
      await listener.callback(data)
      listener.lastUpdate = Date.now()
      listener.retryCount = 0
    } catch (error) {
      console.error(`通知监听器失败: ${dataType}_${identifier}`, error)
      
      // 重试机制
      listener.retryCount++
      if (listener.retryCount < this.config.maxRetries) {
        setTimeout(() => {
          this.notifyListener(dataType, identifier, listener, data)
        }, this.config.retryDelay * listener.retryCount)
      }
    }
  }

  /**
   * 手动触发同步
   * @param {string} dataType 数据类型
   * @param {boolean} forceUpdate 强制更新
   */
  async triggerSync(dataType, forceUpdate = false) {
    if (forceUpdate) {
      // 重置版本号以强制更新
      this.dataVersions.set(dataType, 0)
    }
    
    await this.performSync(dataType)
  }

  /**
   * 批量触发同步
   * @param {Array} dataTypes 数据类型数组
   * @param {boolean} forceUpdate 强制更新
   */
  async batchTriggerSync(dataTypes, forceUpdate = false) {
    const syncPromises = dataTypes.map(dataType => 
      this.triggerSync(dataType, forceUpdate)
    )
    
    await Promise.allSettled(syncPromises)
  }

  /**
   * 初始化页面可见性监听
   */
  initPageVisibilityListener() {
    // 监听小程序显示/隐藏
    const app = getApp()
    if (app) {
      const originalOnShow = app.onShow || function() {}
      const originalOnHide = app.onHide || function() {}

      app.onShow = () => {
        this.pageVisibility.isVisible = true
        originalOnShow.call(app)
      }

      app.onHide = () => {
        this.pageVisibility.isVisible = false
        originalOnHide.call(app)
      }
    }
  }

  /**
   * 注册页面可见性
   */
  registerPageVisibility(pageRoute, isVisible) {
    if (isVisible) {
      this.pageVisibility.visiblePages.add(pageRoute)
    } else {
      this.pageVisibility.visiblePages.delete(pageRoute)
    }
  }

  /**
   * 检查是否应该同步
   */
  shouldSync() {
    // 只有在应用可见且有可见页面时才同步
    return this.pageVisibility.isVisible && this.pageVisibility.visiblePages.size > 0
  }

  /**
   * 初始化全局数据变更监听
   */
  initGlobalDataListener() {
    // 监听全局数据变更事件
    const app = getApp()
    if (app.globalData && app.globalData.dataChangeNotifier) {
      app.globalData.dataChangeNotifier.on('travelPlanCreated', () => {
        // 只有在页面可见时才立即同步
        if (this.shouldSync()) {
          this.batchTriggerSync(['travel_plans', 'current_plan', 'ongoing_plans'], true)
        }
      })

      app.globalData.dataChangeNotifier.on('travelPlanUpdated', () => {
        if (this.shouldSync()) {
          this.batchTriggerSync(['travel_plans', 'current_plan', 'ongoing_plans'], true)
        }
      })

      app.globalData.dataChangeNotifier.on('travelPlanDeleted', () => {
        if (this.shouldSync()) {
          this.batchTriggerSync(['travel_plans', 'current_plan', 'ongoing_plans'], true)
        }
      })

      app.globalData.dataChangeNotifier.on('expenseRecordChanged', () => {
        if (this.shouldSync()) {
          this.triggerSync('travel_plans', true)
        }
      })
    }
  }

  /**
   * 获取同步状态
   */
  getSyncStatus() {
    return {
      ...this.syncStatus,
      activeDataTypes: Array.from(this.syncTimers.keys()),
      listenerCount: Array.from(this.syncListeners.values())
        .reduce((total, listeners) => total + listeners.size, 0)
    }
  }

  /**
   * 清理所有同步
   */
  cleanup() {
    // 停止所有同步定时器
    this.syncTimers.forEach((timer, dataType) => {
      clearInterval(timer)
    })
    
    // 清空所有数据
    this.syncTimers.clear()
    this.syncListeners.clear()
    this.dataVersions.clear()
    
    console.log('实时同步管理器已清理')
  }

  /**
   * 为页面提供便捷的同步方法
   * @param {Object} page 页面实例
   * @param {string} dataType 数据类型
   * @param {Function} updateCallback 更新回调
   */
  bindPageSync(page, dataType, updateCallback) {
    const pageRoute = getCurrentPages().pop().route
    
    // 注册同步
    this.registerSync(dataType, pageRoute, updateCallback)
    
    // 在页面卸载时自动清理
    const originalOnUnload = page.onUnload || function() {}
    page.onUnload = () => {
      this.unregisterSync(dataType, pageRoute)
      originalOnUnload.call(page)
    }
  }
}

// 创建全局实例
const realtimeSyncManager = new RealtimeSyncManager()

export default realtimeSyncManager
