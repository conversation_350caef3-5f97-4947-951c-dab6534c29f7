/**
 * 头像缓存管理器
 * 解决头像加载失败问题，提供本地缓存和智能重试机制
 */

class AvatarCacheManager {
  constructor() {
    // 内存缓存
    this.memoryCache = new Map()
    
    // 本地存储缓存键前缀
    this.storagePrefix = 'avatar_cache_'
    
    // 缓存配置
    this.config = {
      // 内存缓存有效期：30分钟
      memoryCacheTTL: 30 * 60 * 1000,
      // 本地存储缓存有效期：24小时
      storageCacheTTL: 24 * 60 * 60 * 1000,
      // 最大重试次数
      maxRetries: 3,
      // 重试延迟
      retryDelay: 1000
    }
    
    // 正在加载的头像（防止重复请求）
    this.loadingAvatars = new Set()
  }

  /**
   * 获取头像URL（带缓存）
   * @param {string} fileID 云存储文件ID
   * @param {string} openid 用户openid（用于缓存键）
   * @param {boolean} forceRefresh 强制刷新（用户换头像时）
   * @returns {Promise<string>} 头像URL
   */
  async getAvatarUrl(fileID, openid, forceRefresh = false) {
    try {
      // 如果不是云存储文件，直接返回
      if (!fileID || !fileID.startsWith('cloud://')) {
        return fileID || '/images/user.svg'
      }

      const cacheKey = this.getCacheKey(fileID, openid)

      // 如果强制刷新，清除相关缓存
      if (forceRefresh) {
        this.memoryCache.delete(cacheKey)
        this.clearStorageCache(cacheKey)
      }

      // 1. 检查内存缓存
      if (!forceRefresh) {
        const memoryUrl = this.getFromMemoryCache(cacheKey)
        if (memoryUrl) {
          return memoryUrl
        }
      }

      // 2. 检查本地存储缓存
      if (!forceRefresh) {
        const storageUrl = this.getFromStorageCache(cacheKey)
        if (storageUrl) {
          // 同时更新内存缓存
          this.setMemoryCache(cacheKey, storageUrl)
          return storageUrl
        }
      }

      // 3. 防止重复请求
      if (this.loadingAvatars.has(cacheKey)) {
        return await this.waitForLoading(cacheKey)
      }

      // 4. 从云端获取临时链接
      this.loadingAvatars.add(cacheKey)
      const tempUrl = await this.fetchTempUrl(fileID)

      if (tempUrl) {
        // 缓存到内存和本地存储
        this.setMemoryCache(cacheKey, tempUrl)
        this.setStorageCache(cacheKey, tempUrl)

        this.loadingAvatars.delete(cacheKey)
        return tempUrl
      }

      this.loadingAvatars.delete(cacheKey)
      return '/images/user.svg'

    } catch (error) {
      console.error('获取头像URL失败:', error)
      return '/images/user.svg'
    }
  }

  /**
   * 从云端获取临时链接
   * @param {string} fileID 云存储文件ID
   * @returns {Promise<string>} 临时链接
   */
  async fetchTempUrl(fileID) {
    let retries = 0
    
    while (retries < this.config.maxRetries) {
      try {
        const result = await wx.cloud.callFunction({
          name: 'travel',
          data: {
            action: 'getAvatarProxy',
            data: { fileID }
          }
        })

        if (result.result && result.result.success && result.result.data.tempFileURL) {
          return result.result.data.tempFileURL
        }

        throw new Error('获取临时链接失败')

      } catch (error) {
        retries++
        console.error(`获取头像临时链接失败 (${retries}/${this.config.maxRetries}):`, error)
        
        if (retries < this.config.maxRetries) {
          await this.delay(this.config.retryDelay * retries)
        }
      }
    }

    return null
  }

  /**
   * 生成缓存键
   * @param {string} fileID 文件ID
   * @param {string} openid 用户openid
   * @returns {string} 缓存键
   */
  getCacheKey(fileID, openid) {
    // 使用文件ID的哈希值作为缓存键，避免键过长
    const hash = this.simpleHash(fileID)
    return `${openid}_${hash}`
  }

  /**
   * 简单哈希函数
   * @param {string} str 字符串
   * @returns {string} 哈希值
   */
  simpleHash(str) {
    let hash = 0
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i)
      hash = ((hash << 5) - hash) + char
      hash = hash & hash // 转换为32位整数
    }
    return Math.abs(hash).toString(36)
  }

  /**
   * 从内存缓存获取
   * @param {string} key 缓存键
   * @returns {string|null} 缓存的URL
   */
  getFromMemoryCache(key) {
    const cached = this.memoryCache.get(key)
    if (!cached) return null

    // 检查是否过期
    if (Date.now() - cached.timestamp > this.config.memoryCacheTTL) {
      this.memoryCache.delete(key)
      return null
    }

    return cached.url
  }

  /**
   * 设置内存缓存
   * @param {string} key 缓存键
   * @param {string} url URL
   */
  setMemoryCache(key, url) {
    this.memoryCache.set(key, {
      url,
      timestamp: Date.now()
    })

    // 限制内存缓存大小
    if (this.memoryCache.size > 100) {
      const firstKey = this.memoryCache.keys().next().value
      this.memoryCache.delete(firstKey)
    }
  }

  /**
   * 从本地存储缓存获取
   * @param {string} key 缓存键
   * @returns {string|null} 缓存的URL
   */
  getFromStorageCache(key) {
    try {
      const storageKey = this.storagePrefix + key
      const cached = wx.getStorageSync(storageKey)
      
      if (!cached) return null

      const data = JSON.parse(cached)
      
      // 检查是否过期
      if (Date.now() - data.timestamp > this.config.storageCacheTTL) {
        wx.removeStorageSync(storageKey)
        return null
      }

      return data.url

    } catch (error) {
      console.error('读取本地存储缓存失败:', error)
      return null
    }
  }

  /**
   * 设置本地存储缓存
   * @param {string} key 缓存键
   * @param {string} url URL
   */
  setStorageCache(key, url) {
    try {
      const storageKey = this.storagePrefix + key
      const data = {
        url,
        timestamp: Date.now()
      }

      wx.setStorageSync(storageKey, JSON.stringify(data))

    } catch (error) {
      console.error('设置本地存储缓存失败:', error)
    }
  }

  /**
   * 清除单个本地存储缓存
   * @param {string} key 缓存键
   */
  clearStorageCache(key) {
    try {
      const storageKey = this.storagePrefix + key
      wx.removeStorageSync(storageKey)
    } catch (error) {
      console.error('清除本地存储缓存失败:', error)
    }
  }

  /**
   * 等待加载完成
   * @param {string} key 缓存键
   * @returns {Promise<string>} URL
   */
  async waitForLoading(key) {
    let attempts = 0
    const maxAttempts = 30 // 最多等待3秒

    while (this.loadingAvatars.has(key) && attempts < maxAttempts) {
      await this.delay(100)
      attempts++
    }

    // 再次尝试从缓存获取
    return this.getFromMemoryCache(key) || '/images/user.svg'
  }

  /**
   * 延迟函数
   * @param {number} ms 毫秒
   * @returns {Promise}
   */
  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms))
  }

  /**
   * 清理过期缓存
   */
  cleanupExpiredCache() {
    // 清理内存缓存
    const now = Date.now()
    for (const [key, cached] of this.memoryCache.entries()) {
      if (now - cached.timestamp > this.config.memoryCacheTTL) {
        this.memoryCache.delete(key)
      }
    }

    // 清理本地存储缓存（异步执行，不阻塞主线程）
    setTimeout(() => {
      try {
        const info = wx.getStorageInfoSync()
        info.keys.forEach(key => {
          if (key.startsWith(this.storagePrefix)) {
            try {
              const cached = wx.getStorageSync(key)
              const data = JSON.parse(cached)
              
              if (now - data.timestamp > this.config.storageCacheTTL) {
                wx.removeStorageSync(key)
              }
            } catch (error) {
              // 删除损坏的缓存
              wx.removeStorageSync(key)
            }
          }
        })
      } catch (error) {
        console.error('清理本地存储缓存失败:', error)
      }
    }, 0)
  }

  /**
   * 预热头像缓存
   * @param {Array} avatarList 头像列表 [{fileID, openid}]
   */
  async warmupCache(avatarList) {
    if (!Array.isArray(avatarList) || avatarList.length === 0) {
      return
    }

    // 限制并发数量，避免过多请求
    const concurrency = 3
    const chunks = []
    
    for (let i = 0; i < avatarList.length; i += concurrency) {
      chunks.push(avatarList.slice(i, i + concurrency))
    }

    for (const chunk of chunks) {
      await Promise.allSettled(
        chunk.map(({ fileID, openid }) => this.getAvatarUrl(fileID, openid))
      )
    }
  }

  /**
   * 清空所有缓存
   */
  clearAllCache() {
    // 清空内存缓存
    this.memoryCache.clear()

    // 清空本地存储缓存
    try {
      const info = wx.getStorageInfoSync()
      info.keys.forEach(key => {
        if (key.startsWith(this.storagePrefix)) {
          wx.removeStorageSync(key)
        }
      })
    } catch (error) {
      console.error('清空本地存储缓存失败:', error)
    }
  }

  /**
   * 获取缓存统计
   * @returns {Object} 缓存统计信息
   */
  getCacheStats() {
    let storageCount = 0
    try {
      const info = wx.getStorageInfoSync()
      storageCount = info.keys.filter(key => key.startsWith(this.storagePrefix)).length
    } catch (error) {
      console.error('获取存储缓存统计失败:', error)
    }

    return {
      memoryCache: this.memoryCache.size,
      storageCache: storageCount,
      loadingCount: this.loadingAvatars.size
    }
  }
}

// 创建全局实例
const avatarCacheManager = new AvatarCacheManager()

// 定期清理过期缓存（每10分钟）
setInterval(() => {
  avatarCacheManager.cleanupExpiredCache()
}, 10 * 60 * 1000)

export default avatarCacheManager
export { AvatarCacheManager }
